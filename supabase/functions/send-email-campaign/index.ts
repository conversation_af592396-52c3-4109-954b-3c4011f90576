import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { Resend } from "npm:resend@4.0.0";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface EmailCampaignRequest {
  campaignId: string;
  recipients: string[];
  subject: string;
  content: string;
  clubId: string;
}

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log("🚀 Email campaign function called");

    // Get Authorization header to forward to club-email-service
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('Authentifizierung erforderlich. Bitte melden Sie sich an.');
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Initialize Resend
    const resend = new Resend(Deno.env.get("RESEND_API_KEY"));

    const { campaignId, recipients, subject, content, clubId }: EmailCampaignRequest = await req.json();
    console.log(`📧 Processing campaign ${campaignId} for ${recipients.length} recipients`);

    // Get campaign details from database
    const { data: campaign, error: campaignError } = await supabase
      .from('communication_campaigns')
      .select(`
        *,
        clubs!inner(name, settings)
      `)
      .eq('id', campaignId)
      .single();

    if (campaignError || !campaign) {
      throw new Error(`Campaign not found: ${campaignError?.message}`);
    }

    // Use club-specific email service for each recipient
    const emailRequests = recipients.map(async (email: string) => {
      try {
        const emailResponse = await supabase.functions.invoke('club-email-service', {
          headers: {
            'Authorization': authHeader
          },
          body: {
            clubId: campaign.club_id,
            to: email,
            subject: subject,
            html: `
              <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
                <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                  <h1 style="color: #2563eb; margin: 0;">${campaign.clubs.name}</h1>
                  <p style="color: #6b7280; margin: 10px 0 0 0;">Tennis Club Kommunikation</p>
                </div>

                <div style="background-color: white; padding: 20px; border-radius: 8px; border: 1px solid #e5e7eb;">
                  <div style="white-space: pre-wrap; line-height: 1.6; color: #374151;">
                    ${content.replace(/\n/g, '<br>')}
                  </div>
                </div>

                <div style="margin-top: 20px; padding: 20px; background-color: #f8f9fa; border-radius: 8px; text-align: center;">
                  <p style="color: #6b7280; font-size: 12px; margin: 0;">
                    Diese E-Mail wurde über das ${campaign.clubs.name} Kommunikations-System versendet.
                  </p>
                </div>
              </div>
            `
          }
        });

        if (emailResponse.error) {
          throw emailResponse.error;
        }

        return { email, status: 'sent', data: emailResponse.data };
      } catch (error: any) {
        console.error(`Failed to send to ${email}:`, error);
        return { email, status: 'failed', error: error.message };
      }
    });

    // Process all email requests with batching for rate limiting
    const batchSize = 10;
    const emailBatches = [];
    for (let i = 0; i < emailRequests.length; i += batchSize) {
      emailBatches.push(emailRequests.slice(i, i + batchSize));
    }

    let successCount = 0;
    let errorCount = 0;
    const errors: string[] = [];

    // Process each batch with delay
    for (const batch of emailBatches) {
      const batchResults = await Promise.allSettled(batch);
      
      batchResults.forEach(result => {
        if (result.status === 'fulfilled') {
          if (result.value.status === 'sent') {
            successCount++;
          } else {
            errorCount++;
            errors.push(result.value.error || 'Unknown error');
          }
        } else {
          errorCount++;
          errors.push(result.reason || 'Promise rejected');
        }
      });

      // Add delay between batches to respect rate limits
      if (emailBatches.indexOf(batch) < emailBatches.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    // Update campaign status and analytics
    await supabase
      .from('communication_campaigns')
      .update({
        status: 'sent',
        sent_at: new Date().toISOString(),
        analytics: {
          sent: successCount,
          failed: errorCount,
          total_recipients: recipients.length
        }
      })
      .eq('id', campaignId);

    // Log analytics
    await supabase
      .from('communication_analytics')
      .insert({
        club_id: clubId,
        campaign_id: campaignId,
        event_type: 'campaign_sent',
        channel: 'email',
        metadata: {
          recipients_count: recipients.length,
          success_count: successCount,
          error_count: errorCount,
          errors: errors
        }
      });

    console.log(`📊 Campaign completed: ${successCount} sent, ${errorCount} failed`);

    return new Response(JSON.stringify({
      success: true,
      message: `Campaign sent successfully`,
      stats: {
        sent: successCount,
        failed: errorCount,
        total: recipients.length
      }
    }), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        ...corsHeaders,
      },
    });

  } catch (error: any) {
    console.error("💥 Error in send-email-campaign function:", error);
    return new Response(
      JSON.stringify({ 
        error: error.message,
        success: false 
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      }
    );
  }
};

serve(handler);