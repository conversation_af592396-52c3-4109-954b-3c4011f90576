import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.53.0';
import nodemailer from 'npm:nodemailer@6.9.10';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface EmailRequest {
  clubId: string;
  to: string | string[];
  subject: string;
  html?: string;
  text?: string;
  from?: string;
  replyTo?: string;
}

interface SmtpConfig {
  enabled: boolean;
  host: string;
  port: number;
  secure: boolean;
  user: string;
  password: string;
  from_name: string;
  from_email: string;
  reply_to?: string;
}

// Function to check if user has permission to send emails for a club
async function checkEmailPermissions(supabase: any, userId: string, clubId: string): Promise<void> {
  // Check if user is a super admin (can send emails for any club)
  const { data: isSuperAdmin, error: superAdminError } = await supabase
    .rpc('has_meta_admin_role', {
      _user_id: userId,
      _role: 'SUPER_ADMIN'
    });

  if (superAdminError) {
    console.error('Error checking super admin role:', superAdminError);
  }

  if (isSuperAdmin) {
    console.log('User is super admin, allowing email send');
    return; // Super admin can send emails for any club
  }

  // Check if user is admin of the specific club
  const { data: isClubAdmin, error: clubAdminError } = await supabase
    .rpc('is_club_admin_for_club', {
      _club_id: clubId
    });

  if (clubAdminError) {
    console.error('Error checking club admin role:', clubAdminError);
    throw new Error('Fehler bei der Berechtigungsprüfung. Bitte versuchen Sie es erneut.');
  }

  if (!isClubAdmin) {
    throw new Error('Keine Berechtigung zum Versenden von E-Mails für diesen Club. Nur Club-Admins und Super-Admins können E-Mails versenden.');
  }

  console.log('User is club admin, allowing email send');
}

// Function to get intelligent TLS configuration based on SMTP port
function getTransportConfig(smtpConfig: SmtpConfig): any {
  const config: any = {
    host: smtpConfig.host,
    port: smtpConfig.port,
    auth: {
      user: smtpConfig.user,
      pass: smtpConfig.password
    }
  };

  // Port-based TLS configuration
  if (smtpConfig.port === 465) {
    // SSL/TLS - direkte verschlüsselte Verbindung
    config.secure = true;
    console.log('Using SSL/TLS (port 465)');
  } else if (smtpConfig.port === 587) {
    // STARTTLS - Upgrade von unverschlüsselt zu verschlüsselt
    config.secure = false;
    config.requireTLS = true;
    console.log('Using STARTTLS (port 587)');
  } else if (smtpConfig.port === 25) {
    // Unverschlüsselt (Legacy)
    config.secure = false;
    config.requireTLS = false;
    console.log('Using unencrypted connection (port 25)');
  } else {
    // Fallback: Verwende secure-Flag aus Konfiguration
    config.secure = smtpConfig.secure;
    console.log(`Using fallback configuration: secure=${smtpConfig.secure}`);
  }

  // Provider-spezifische TLS-Optionen
  if (smtpConfig.host.includes('gmail')) {
    config.tls = {
      rejectUnauthorized: false // Für Gmail App-Passwörter
    };
    console.log('Applied Gmail-specific TLS settings');
  }

  return config;
}

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { clubId, to, subject, html, text, from, replyTo }: EmailRequest = await req.json();

    // Initialize Supabase client with service role for admin operations
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Initialize Supabase client for user authentication
    const authHeader = req.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new Error('Authentifizierung erforderlich. Bitte melden Sie sich an.');
    }

    const token = authHeader.replace('Bearer ', '');

    // Check if this is a service role token (for internal system operations)
    const serviceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
    if (token === serviceRoleKey) {
      console.log('Service role token detected, skipping user authentication');
      // Service role has full permissions, skip user checks
    } else {
      // Regular user authentication
      const supabaseUser = createClient(
        Deno.env.get('SUPABASE_URL') ?? '',
        Deno.env.get('SUPABASE_ANON_KEY') ?? ''
      );

      // Verify JWT token and get user
      const { data: { user }, error: authError } = await supabaseUser.auth.getUser(token);
      if (authError || !user) {
        throw new Error('Ungültiger Authentifizierungstoken. Bitte melden Sie sich erneut an.');
      }

      // Check if user has permission to send emails for this club
      await checkEmailPermissions(supabaseAdmin, user.id, clubId);
    }

    // Get club SMTP configuration
    const { data: club, error: clubError } = await supabaseAdmin
      .from('clubs')
      .select('settings, name')
      .eq('id', clubId)
      .single();

    if (clubError || !club) {
      throw new Error(`Club not found: ${clubError?.message}`);
    }

    const smtpConfig = club.settings?.smtp;

    // SMTP must be enabled and configured
    if (!smtpConfig?.enabled || !smtpConfig.host || !smtpConfig.user) {
      throw new Error('SMTP ist nicht konfiguriert oder deaktiviert. Bitte konfiguriere SMTP in den Club-Einstellungen.');
    }

    // Send via Nodemailer SMTP
    return await sendViaNodemailer(smtpConfig, {
      to,
      subject,
      html,
      text,
      from: from || `${smtpConfig.from_name} <${smtpConfig.from_email}>`,
      replyTo: replyTo || smtpConfig.reply_to
    });

  } catch (error: any) {
    console.error('Error in club-email-service:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      }
    );
  }
};

// Function to validate and sanitize email headers
function validateAndSanitizeHeaders(emailData: any, smtpConfig: SmtpConfig): any {
  // Function to remove control characters and validate headers
  const sanitizeHeader = (value: string): string => {
    if (!value) return '';
    // Remove control characters (including newlines) that could cause header injection
    return value.replace(/[\r\n\t\x00-\x1f\x7f]/g, ' ').trim();
  };

  // Function to properly encode display names with special characters
  const encodeDisplayName = (name: string): string => {
    if (!name) return '';
    // Check if name contains non-ASCII characters or special characters
    if (/[^\x20-\x7E]/.test(name) || /[<>@,;:\\"/\[\]?=]/.test(name)) {
      // Use RFC 2047 encoding for non-ASCII or special characters
      // Convert to UTF-8 bytes and then to base64
      const utf8Bytes = new TextEncoder().encode(name);
      const base64 = btoa(String.fromCharCode(...utf8Bytes));
      return `=?UTF-8?B?${base64}?=`;
    }
    return name;
  };

  // Validate and sanitize from address
  const fromName = sanitizeHeader(smtpConfig.from_name || '');
  const fromEmail = sanitizeHeader(smtpConfig.from_email || '');

  if (!fromEmail || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(fromEmail)) {
    throw new Error('Ungültige Absender-E-Mail-Adresse in der SMTP-Konfiguration');
  }

  // Construct proper From header
  let fromHeader = fromEmail;
  if (fromName) {
    const encodedName = encodeDisplayName(fromName);
    fromHeader = `${encodedName} <${fromEmail}>`;
  }

  // Validate and sanitize other headers
  const subject = sanitizeHeader(emailData.subject || '');
  if (!subject) {
    throw new Error('E-Mail-Betreff darf nicht leer sein');
  }

  // Validate recipient addresses
  const validateEmail = (email: string): boolean => {
    const sanitized = sanitizeHeader(email);
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(sanitized);
  };

  let recipients: string[];
  if (Array.isArray(emailData.to)) {
    recipients = emailData.to.map((email: string) => sanitizeHeader(email));
  } else {
    recipients = [sanitizeHeader(emailData.to)];
  }

  // Validate all recipient emails
  for (const email of recipients) {
    if (!validateEmail(email)) {
      throw new Error(`Ungültige Empfänger-E-Mail-Adresse: ${email}`);
    }
  }

  // Validate reply-to if provided
  let replyTo = '';
  if (emailData.replyTo || smtpConfig.reply_to) {
    replyTo = sanitizeHeader(emailData.replyTo || smtpConfig.reply_to || '');
    if (replyTo && !validateEmail(replyTo)) {
      throw new Error(`Ungültige Reply-To-E-Mail-Adresse: ${replyTo}`);
    }
  }

  return {
    from: fromHeader,
    to: recipients.join(', '),
    subject: subject,
    html: emailData.html || '',
    text: emailData.text || '',
    replyTo: replyTo || undefined
  };
}

async function sendViaNodemailer(smtpConfig: SmtpConfig, emailData: any): Promise<Response> {
  try {
    console.log('Sending email via Nodemailer:', {
      host: smtpConfig.host,
      port: smtpConfig.port,
      secure: smtpConfig.secure,
      user: smtpConfig.user
    });

    // Validate and sanitize email headers
    const sanitizedEmailData = validateAndSanitizeHeaders(emailData, smtpConfig);
    console.log('Email headers validated and sanitized');

    // Get intelligent TLS configuration
    const transportConfig = getTransportConfig(smtpConfig);

    // Add additional configuration for better header handling
    transportConfig.pool = false; // Disable connection pooling for better reliability
    transportConfig.maxConnections = 1;
    transportConfig.maxMessages = 1;

    // Create Nodemailer transporter
    const transporter = nodemailer.createTransport(transportConfig);

    // Prepare email data with sanitized headers
    const mailOptions = {
      from: sanitizedEmailData.from,
      to: sanitizedEmailData.to,
      subject: sanitizedEmailData.subject,
      html: sanitizedEmailData.html,
      text: sanitizedEmailData.text,
      replyTo: sanitizedEmailData.replyTo,
      // Add additional headers for better compatibility
      headers: {
        'X-Mailer': 'Tennis Booking Hub',
        'X-Priority': '3',
        'MIME-Version': '1.0'
      }
    };

    console.log('Sending email with headers:', {
      from: mailOptions.from,
      to: mailOptions.to,
      subject: mailOptions.subject,
      replyTo: mailOptions.replyTo
    });

    // Send email
    const result = await transporter.sendMail(mailOptions);

    console.log('Email sent successfully via Nodemailer:', result.messageId);

    return new Response(
      JSON.stringify({
        success: true,
        message: 'E-Mail erfolgreich über SMTP gesendet',
        messageId: result.messageId,
        provider: 'nodemailer',
        host: smtpConfig.host,
        recipients: Array.isArray(emailData.to) ? emailData.to.length : 1
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      }
    );

  } catch (error: any) {
    console.error('Nodemailer sending failed:', error);

    // Spezifische Header-Fehlerbehandlung (521 Fehler in Mail-Header)
    if (error.message?.includes('521') || error.message?.includes('Mail-Header') ||
        error.message?.includes('header') || error.responseCode === 521) {
      throw new Error(`E-Mail-Header-Fehler: Die E-Mail-Header sind ungültig oder enthalten unerlaubte Zeichen.

Mögliche Ursachen:
1. Sonderzeichen im Absendernamen (ä, ö, ü, ß)
2. Ungültige E-Mail-Adressen
3. Fehlende oder ungültige Header-Felder

Bitte prüfen Sie:
- Absendername und E-Mail-Adresse in den SMTP-Einstellungen
- Empfänger-E-Mail-Adressen
- E-Mail-Betreff auf Sonderzeichen

Ursprünglicher Fehler: ${error.message}`);
    }

    // Spezifische TLS-Fehlerbehandlung
    if (error.code === 'ECONNECTION' || error.message.includes('TLS')) {
      throw new Error(`TLS-Verbindungsfehler: ${error.message}.
Prüfen Sie die Port-Konfiguration (465=SSL, 587=STARTTLS).`);
    }

    // Gmail-spezifische Fehlerbehandlung
    if (error.message?.includes('530') || error.message?.includes('authentication') ||
        (smtpConfig.host.includes('gmail') && error.message?.includes('Invalid login'))) {
      throw new Error(`Gmail SMTP-Fehler: Für Gmail ist ein App-spezifisches Passwort erforderlich.

Bitte:
1. Gehen Sie zu https://myaccount.google.com/apppasswords
2. Erstellen Sie ein App-Passwort für "E-Mail"
3. Verwenden Sie dieses Passwort in den SMTP-Einstellungen
4. Stellen Sie sicher, dass 2-Faktor-Authentifizierung aktiviert ist

Ursprünglicher Fehler: ${error.message}`);
    }

    // Deutsche E-Mail-Provider spezifische Fehlerbehandlung
    if (smtpConfig.host.includes('ionos') || smtpConfig.host.includes('1und1') ||
        smtpConfig.host.includes('strato') || smtpConfig.host.includes('web.de') ||
        smtpConfig.host.includes('gmx')) {
      throw new Error(`Deutscher E-Mail-Provider Fehler: ${error.message}

Deutsche E-Mail-Provider haben oft strenge Header-Validierung.
Bitte stellen Sie sicher, dass:
1. Der Absendername keine Sonderzeichen enthält
2. Die From-E-Mail-Adresse korrekt konfiguriert ist
3. Alle E-Mail-Adressen gültig sind

Ursprünglicher Fehler: ${error.message}`);
    }

    // Allgemeine SMTP-Fehler
    throw new Error(`SMTP-Versendung fehlgeschlagen: ${error.message}`);
  }
}

serve(handler);